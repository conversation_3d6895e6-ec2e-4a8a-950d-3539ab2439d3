<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>聊天系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f5f5f5;
            height: 100vh;
            display: flex;
            flex-direction: column;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .header h1 {
            font-size: 24px;
        }

        .user-info {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .user-name {
            font-weight: 500;
        }

        .logout-btn {
            background: rgba(255,255,255,0.2);
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 20px;
            cursor: pointer;
            transition: background 0.3s;
        }

        .logout-btn:hover {
            background: rgba(255,255,255,0.3);
        }

        .chat-container {
            flex: 1;
            display: flex;
            max-height: calc(100vh - 70px);
        }

        .sidebar {
            width: 300px;
            background: white;
            border-right: 1px solid #e0e0e0;
            display: flex;
            flex-direction: column;
        }

        .sidebar-header {
            padding: 20px;
            border-bottom: 1px solid #e0e0e0;
            background: #fafafa;
        }

        .sidebar-header h3 {
            color: #333;
            margin-bottom: 10px;
        }

        .online-users {
            flex: 1;
            overflow-y: auto;
        }

        .user-item {
            padding: 12px 20px;
            border-bottom: 1px solid #f0f0f0;
            cursor: pointer;
            transition: background 0.2s;
        }

        .user-item:hover {
            background: #f5f5f5;
        }

        .user-item.active {
            background: #e3f2fd;
            border-left: 3px solid #2196f3;
        }

        .main-chat {
            flex: 1;
            display: flex;
            flex-direction: column;
            background: white;
        }

        .chat-header {
            padding: 20px;
            border-bottom: 1px solid #e0e0e0;
            background: #fafafa;
        }

        .chat-messages {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
            background: #f9f9f9;
        }

        .message {
            margin-bottom: 15px;
            display: flex;
            align-items: flex-start;
            gap: 10px;
        }

        .message.own {
            flex-direction: row-reverse;
        }

        .message-content {
            max-width: 70%;
            padding: 12px 16px;
            border-radius: 18px;
            background: white;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }

        .message.own .message-content {
            background: #667eea;
            color: white;
        }

        .message-info {
            font-size: 12px;
            color: #666;
            margin-bottom: 5px;
        }

        .message.own .message-info {
            color: rgba(255,255,255,0.8);
        }

        .chat-input {
            padding: 20px;
            border-top: 1px solid #e0e0e0;
            background: white;
        }

        .input-container {
            display: flex;
            gap: 10px;
            align-items: center;
        }

        .message-input {
            flex: 1;
            padding: 12px 16px;
            border: 1px solid #ddd;
            border-radius: 25px;
            font-size: 14px;
            outline: none;
        }

        .message-input:focus {
            border-color: #667eea;
        }

        .send-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 25px;
            cursor: pointer;
            font-weight: 500;
            transition: transform 0.2s;
        }

        .send-btn:hover {
            transform: translateY(-1px);
        }

        .status {
            padding: 10px 20px;
            background: #e8f5e8;
            color: #2e7d32;
            text-align: center;
            font-size: 14px;
        }

        .status.error {
            background: #ffebee;
            color: #c62828;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>💬 聊天系统</h1>
        <div class="user-info">
            <span class="user-name">欢迎，<span th:text="${username ?: 'Guest'}">用户</span></span>
            <span th:if="${tokenType}" class="token-info" style="font-size: 12px; opacity: 0.8;">
                (OAuth2 认证)
            </span>
            <form th:action="@{/logout}" method="post" style="display: inline;">
                <button type="submit" class="logout-btn">退出登录</button>
            </form>
        </div>
    </div>

    <div class="chat-container">
        <div class="sidebar">
            <div class="sidebar-header">
                <h3>在线用户</h3>
                <div id="onlineCount">0 人在线</div>
            </div>
            <div class="online-users" id="onlineUsers">
                <!-- 在线用户列表 -->
            </div>
        </div>

        <div class="main-chat">
            <div class="chat-header">
                <h3>聊天室</h3>
            </div>
            
            <div id="status" class="status" style="display: none;"></div>
            
            <div class="chat-messages" id="messages">
                <!-- 聊天消息 -->
            </div>

            <div class="chat-input">
                <div class="input-container">
                    <input type="text" id="messageInput" class="message-input" 
                           placeholder="输入消息..." maxlength="500">
                    <button id="sendBtn" class="send-btn">发送</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        const username = /*[[${username}]]*/ 'Guest';
        const accessToken = /*[[${accessToken}]]*/ '';
        const tokenType = /*[[${tokenType}]]*/ 'Bearer';
        let websocket = null;
        let isConnected = false;

        // 初始化WebSocket连接
        function initWebSocket() {
            try {
                websocket = new WebSocket('ws://localhost:9090/ws');
                
                websocket.onopen = function() {
                    console.log('WebSocket连接已建立');
                    isConnected = true;
                    showStatus('已连接到服务器', false);
                    
                    // 使用JWT token进行认证
                    const authMessage = {
                        type: 'auth',
                        data: {
                            token: accessToken,
                            tokenType: tokenType,
                            username: username
                        },
                        timestamp: Date.now()
                    };
                    
                    console.log('发送JWT认证消息:', {
                        type: 'auth',
                        username: username,
                        tokenType: tokenType,
                        tokenLength: accessToken ? accessToken.length : 0
                    });
                    
                    websocket.send(JSON.stringify(authMessage));
                };

                websocket.onmessage = function(event) {
                    const message = JSON.parse(event.data);
                    handleMessage(message);
                };

                websocket.onclose = function() {
                    console.log('WebSocket连接已关闭');
                    isConnected = false;
                    showStatus('连接已断开，正在重连...', true);
                    
                    // 3秒后重连
                    setTimeout(initWebSocket, 3000);
                };

                websocket.onerror = function(error) {
                    console.error('WebSocket错误:', error);
                    showStatus('连接错误', true);
                };
            } catch (error) {
                console.error('WebSocket初始化失败:', error);
                showStatus('连接失败', true);
            }
        }

        // 处理接收到的消息
        function handleMessage(message) {
            switch(message.type) {
                case 'auth_success':
                    console.log('认证成功');
                    showStatus('认证成功', false);
                    setTimeout(() => hideStatus(), 2000);
                    break;
                case 'auth_failure':
                    console.log('认证失败:', message.data.message);
                    showStatus('认证失败: ' + message.data.message, true);
                    break;
                case 'chat_message':
                    displayMessage(message.data);
                    break;
                case 'user_list':
                    updateUserList(message.data.users);
                    break;
                case 'system_message':
                    displaySystemMessage(message.data.message);
                    break;
            }
        }

        // 发送消息
        function sendMessage() {
            const input = document.getElementById('messageInput');
            const messageText = input.value.trim();
            
            if (!messageText || !isConnected) {
                return;
            }

            const message = {
                type: 'chat_message',
                data: {
                    content: messageText,
                    targetType: 'ROOM',
                    targetId: 'default'
                },
                timestamp: Date.now()
            };

            websocket.send(JSON.stringify(message));
            input.value = '';
        }

        // 显示消息
        function displayMessage(data) {
            const messagesDiv = document.getElementById('messages');
            const messageDiv = document.createElement('div');
            messageDiv.className = 'message' + (data.sender === username ? ' own' : '');
            
            messageDiv.innerHTML = `
                <div class="message-content">
                    <div class="message-info">${data.sender} • ${formatTime(data.timestamp)}</div>
                    <div>${escapeHtml(data.content)}</div>
                </div>
            `;
            
            messagesDiv.appendChild(messageDiv);
            messagesDiv.scrollTop = messagesDiv.scrollHeight;
        }

        // 显示系统消息
        function displaySystemMessage(message) {
            const messagesDiv = document.getElementById('messages');
            const messageDiv = document.createElement('div');
            messageDiv.className = 'message system';
            messageDiv.innerHTML = `
                <div class="message-content" style="background: #f0f0f0; color: #666; text-align: center;">
                    ${escapeHtml(message)}
                </div>
            `;
            messagesDiv.appendChild(messageDiv);
            messagesDiv.scrollTop = messagesDiv.scrollHeight;
        }

        // 更新用户列表
        function updateUserList(users) {
            const userListDiv = document.getElementById('onlineUsers');
            const countDiv = document.getElementById('onlineCount');
            
            countDiv.textContent = users.length + ' 人在线';
            
            userListDiv.innerHTML = '';
            users.forEach(user => {
                const userDiv = document.createElement('div');
                userDiv.className = 'user-item';
                userDiv.textContent = user.nickname || user.username;
                userListDiv.appendChild(userDiv);
            });
        }

        // 显示状态
        function showStatus(message, isError) {
            const statusDiv = document.getElementById('status');
            statusDiv.textContent = message;
            statusDiv.className = 'status' + (isError ? ' error' : '');
            statusDiv.style.display = 'block';
        }

        // 隐藏状态
        function hideStatus() {
            document.getElementById('status').style.display = 'none';
        }

        // 格式化时间
        function formatTime(timestamp) {
            const date = new Date(timestamp);
            return date.toLocaleTimeString();
        }

        // HTML转义
        function escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }

        // 事件监听
        document.getElementById('sendBtn').addEventListener('click', sendMessage);
        document.getElementById('messageInput').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                sendMessage();
            }
        });

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            initWebSocket();
        });

        // 页面卸载时关闭连接
        window.addEventListener('beforeunload', function() {
            if (websocket) {
                websocket.close();
            }
        });
    </script>
</body>
</html>