package com.xiang.service.impl;

import com.xiang.entity.UserEntity;
import com.xiang.mapper.UserMapper;
import com.xiang.service.UserService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class UserServiceImpl implements UserService {

    private final UserMapper userMapper;

    @Override
    public UserEntity findByUsername(String username) {
        log.debug("Finding user by username: {}", username);
        return userMapper.findByUsername(username);
    }

    @Override
    public UserEntity findById(Long id) {
        log.debug("Finding user by id: {}", id);
        return userMapper.selectById(id);
    }
}