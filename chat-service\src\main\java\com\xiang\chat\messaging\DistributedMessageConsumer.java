package com.xiang.chat.messaging;

import com.alibaba.fastjson2.JSON;
import com.xiang.chat.constants.MessageConstants;
import com.xiang.chat.dto.WebSocketMessage;
import com.xiang.chat.service.ConnectionManager;
import com.xiang.chat.service.DistributedConnectionManager;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.annotation.ConsumeMode;
import org.apache.rocketmq.spring.annotation.MessageModel;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * 分布式消息消费者
 * 处理跨节点的消息转发和广播
 */
@Slf4j
@Component
@RequiredArgsConstructor
@RocketMQMessageListener(
    topic = MessageConstants.TOPIC_NOTIFICATION,
    consumerGroup = "chat-distributed-group",
    consumeMode = ConsumeMode.CONCURRENTLY,
    messageModel = MessageModel.CLUSTERING,
    selectorExpression = "MESSAGE_FORWARD || ROOM_BROADCAST || USER_STATUS_CHANGED"
)
public class DistributedMessageConsumer implements RocketMQListener<String> {

    private final ConnectionManager localConnectionManager;
    private final DistributedConnectionManager distributedConnectionManager;

    @Override
    public void onMessage(String messageBody) {
        try {
            log.info("接收到分布式消息: {}", messageBody);

            Map<String, Object> messageData = parseMessage(messageBody);
            if (messageData != null) {
                handleMessage(messageData);
            }
        } catch (Exception e) {
            log.error("处理分布式消息失败", e);
        }
    }

    private Map<String, Object> parseMessage(String messageBody) {
        try {
            @SuppressWarnings("unchecked")
            Map<String, Object> result = JSON.parseObject(messageBody, Map.class);
            return result;
        } catch (Exception e) {
            log.error("解析分布式消息失败: {}", messageBody, e);
            return null;
        }
    }

    private void handleMessage(Map<String, Object> message) throws Exception {
        // 只处理事件格式的消息（包含eventType字段）
        String eventType = (String) message.get("eventType");
        if (eventType == null) {
            log.warn("消息格式不正确，缺少eventType字段: {}", message);
            return;
        }

        String nodeId = (String) message.get("nodeId");
        String currentNodeId = distributedConnectionManager.getCurrentNodeId();

        // 忽略来自当前节点的消息，避免循环处理
        if (currentNodeId.equals(nodeId)) {
            log.debug("忽略来自当前节点的事件: eventType={}, nodeId={}", eventType, nodeId);
            return;
        }

        log.debug("处理分布式事件: eventType={}, nodeId={}", eventType, nodeId);

        switch (eventType) {
            case "UserStatusChangedEvent":
                handleUserStatusChangedEvent(message);
                break;
            case "MessageForwardEvent":
                handleMessageForwardEvent(message);
                break;
            case "RoomBroadcastEvent":
                handleRoomBroadcastEvent(message);
                break;
            default:
                log.warn("未知的事件类型: {}", eventType);
        }
    }

    /**
     * 处理消息转发事件
     */
    private void handleMessageForwardEvent(Map<String, Object> message) {
        try {
            String targetNodeId = (String) message.get("targetNodeId");
            String currentNodeId = distributedConnectionManager.getCurrentNodeId();

            // 检查是否是发送给当前节点的消息
            if (!currentNodeId.equals(targetNodeId)) {
                log.debug("消息不是发送给当前节点: targetNodeId={}, currentNodeId={}",
                    targetNodeId, currentNodeId);
                return;
            }

            Long userId = Long.parseLong(message.get("userId").toString());
            @SuppressWarnings("unchecked")
            Map<String, Object> messageData = (Map<String, Object>) message.get("message");
            WebSocketMessage wsMessage = JSON.parseObject(JSON.toJSONString(messageData), WebSocketMessage.class);

            // 转发消息给本地用户
            distributedConnectionManager.handleMessageForward(userId, wsMessage);

            log.debug("处理消息转发完成: userId={}, targetNodeId={}", userId, targetNodeId);

        } catch (Exception e) {
            log.error("处理消息转发失败: message={}", message, e);
        }
    }

    /**
     * 处理房间广播事件
     */
    private void handleRoomBroadcastEvent(Map<String, Object> message) {
        try {
            String roomId = (String) message.get("roomId");
            @SuppressWarnings("unchecked")
            Map<String, Object> messageData = (Map<String, Object>) message.get("message");
            WebSocketMessage wsMessage = JSON.parseObject(JSON.toJSONString(messageData), WebSocketMessage.class);

            Object excludeUserIdObj = message.get("excludeUserId");
            Long excludeUserId = null;
            if (excludeUserIdObj != null && !excludeUserIdObj.toString().equals("0")) {
                excludeUserId = Long.parseLong(excludeUserIdObj.toString());
            }

            // 在当前节点广播消息
            int sentCount = localConnectionManager.broadcastToRoom(roomId, wsMessage, excludeUserId);

            log.debug("处理房间广播完成: roomId={}, sentCount={}", roomId, sentCount);

        } catch (Exception e) {
            log.error("处理房间广播失败: message={}", message, e);
        }
    }



    /**
     * 处理用户状态变更事件（事件格式）
     */
    private void handleUserStatusChangedEvent(Map<String, Object> message) {
        try {
            Object targetUserIdObj = message.get("targetUserId");
            Object userIdObj = message.get("userId");
            String status = (String) message.get("status");
            String nodeId = (String) message.get("nodeId");

            // 优先使用targetUserId，如果没有则使用userId
            Long userId = null;
            if (targetUserIdObj != null) {
                userId = Long.parseLong(targetUserIdObj.toString());
            } else if (userIdObj != null) {
                userId = Long.parseLong(userIdObj.toString());
            }

            if (userId == null) {
                log.warn("用户状态变更事件缺少用户ID: {}", message);
                return;
            }

            log.debug("用户状态变更事件: userId={}, status={}, nodeId={}", userId, status, nodeId);

            // 处理用户状态变更
            if ("ONLINE".equals(status)) {
                // 用户上线，记录日志
                log.info("收到用户上线事件: userId={}, nodeId={}", userId, nodeId);
            } else if ("OFFLINE".equals(status)) {
                // 用户下线，记录日志
                log.info("收到用户下线事件: userId={}, nodeId={}", userId, nodeId);
            }

            // 这里可以添加其他用户状态变更的处理逻辑
            // 比如发送通知给其他用户、更新用户在线状态缓存等

        } catch (Exception e) {
            log.error("处理用户状态变更事件失败: message={}", message, e);
        }
    }


}
