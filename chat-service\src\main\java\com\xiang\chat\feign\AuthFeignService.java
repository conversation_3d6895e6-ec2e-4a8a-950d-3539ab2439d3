package com.xiang.chat.feign;

import com.xiang.chat.code.R;
import com.xiang.chat.dto.UserInfo;
import com.xiang.chat.dto.ValidationResult;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;


@FeignClient(name = "auth-service", fallback = AuthFeignService.AuthFeignServiceFallback.class)
public interface AuthFeignService {

    /**
     * 验证JWT token
     */
    @PostMapping("/api/jwt/validate")
    R<ValidationResult> validateToken(@RequestParam("token") String token);

    /**
     * 获取用户信息
     */
    @GetMapping("/api/jwt/userinfo")
    R<UserInfo> getUserInfo();

    /**
     * Fallback实现
     */
    class AuthFeignServiceFallback implements AuthFeignService {

        @Override
        public R<ValidationResult> validateToken(String token) {
            return R.error("认证服务不可用，请稍后重试");
        }

        @Override
        public R<UserInfo> getUserInfo() {
            return R.error("认证服务不可用");
        }
    }
}
