package com.xiang.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.xiang.entity.UserEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

@Mapper
public interface UserMapper extends BaseMapper<UserEntity> {
    
    @Select("SELECT * FROM user WHERE username = #{username} AND deleted = 0")
    UserEntity findByUsername(String username);
}