package com.xiang.chat.service.impl;

import com.alibaba.fastjson2.JSON;
import com.xiang.chat.dto.WebSocketMessage;
import com.xiang.chat.model.dto.UserConnection;
import com.xiang.chat.model.AuthResult;
import com.xiang.chat.service.ConnectionManager;
import com.xiang.chat.service.DistributedConnectionManager;
import com.xiang.chat.service.JwtValidationService;
import io.netty.channel.Channel;
import io.netty.handler.codec.http.websocketx.TextWebSocketFrame;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 连接管理器实现
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ConnectionManagerImpl implements ConnectionManager {

    private final RedisTemplate<String, Object> redisTemplate;
    private final JwtValidationService jwtValidationService;
    private DistributedConnectionManager distributedConnectionManager;

    // 解决循环依赖的setter方法
    public void setDistributedConnectionManager(DistributedConnectionManager distributedConnectionManager) {
        this.distributedConnectionManager = distributedConnectionManager;
    }

    // 本地连接缓存：Channel -> UserConnection
    private final ConcurrentHashMap<Channel, UserConnection> channelConnections = new ConcurrentHashMap<>();
    
    // 本地连接缓存：UserId -> UserConnection
    private final ConcurrentHashMap<Long, UserConnection> userConnections = new ConcurrentHashMap<>();
    
    // 房间用户映射：RoomId -> Set<UserId>
    private final ConcurrentHashMap<String, Set<Long>> roomUsers = new ConcurrentHashMap<>();
    
    // 用户房间映射：UserId -> Set<RoomId>
    private final ConcurrentHashMap<Long, Set<String>> userRooms = new ConcurrentHashMap<>();

    @Override
    public void addConnection(Channel channel, Long userId) {
        try {
            // 如果用户已有连接，先移除旧连接
            UserConnection existingConnection = userConnections.get(userId);
            if (existingConnection != null) {
                removeConnection(existingConnection.getChannel());
            }

            // 创建新连接
            UserConnection connection = new UserConnection(userId, channel);
            connection.setStatus(UserConnection.ConnectionStatus.AUTHENTICATED);
            connection.setIpAddress(getChannelRemoteAddress(channel));

            // 添加到本地缓存
            channelConnections.put(channel, connection);
            userConnections.put(userId, connection);

            // 同步到Redis
            syncConnectionToRedis(connection);

            // 注册到分布式连接管理器
            if (distributedConnectionManager != null) {
                log.info("开始注册到分布式连接管理器: userId={}", userId);
                distributedConnectionManager.registerUserConnection(userId, distributedConnectionManager.getCurrentNodeId());
                log.info("分布式连接管理器注册完成: userId={}", userId);
            }

            // 注意：离线消息发送由上层调用者负责，避免重复调用
            // offlineMessageService.sendOfflineMessagesToUser(userId);

            log.info("用户连接已添加: userId={}, channelId={}, ip={}", 
                userId, channel.id().asShortText(), connection.getIpAddress());

        } catch (Exception e) {
            log.error("添加用户连接失败: userId={}, channelId={}", 
                userId, channel.id().asShortText(), e);
        }
    }

    @Override
    public void removeConnection(Channel channel) {
        try {
            UserConnection connection = channelConnections.remove(channel);
            if (connection == null) {
                return;
            }

            Long userId = connection.getUserId();
            userConnections.remove(userId);

            // 从所有房间中移除用户
            Set<String> joinedRooms = connection.getJoinedRooms();
            if (joinedRooms != null) {
                for (String roomId : joinedRooms) {
                    leaveRoomInternal(userId, roomId);
                }
            }

            // 从Redis中移除连接信息
            removeConnectionFromRedis(userId);

            // 从分布式连接管理器注销
            if (distributedConnectionManager != null) {
                distributedConnectionManager.unregisterUserConnection(userId, distributedConnectionManager.getCurrentNodeId());
            }

            log.info("用户连接已移除: userId={}, channelId={}", 
                userId, channel.id().asShortText());

        } catch (Exception e) {
            log.error("移除用户连接失败: channelId={}", channel.id().asShortText(), e);
        }
    }

    @Override
    public UserConnection getConnectionByUserId(Long userId) {
        return userConnections.get(userId);
    }

    @Override
    public UserConnection getConnectionByChannel(Channel channel) {
        return channelConnections.get(channel);
    }

    @Override
    public Long getUserId(Channel channel) {
        UserConnection connection = channelConnections.get(channel);
        return connection != null ? connection.getUserId() : null;
    }

    @Override
    public Long authenticateUser(String token) {
        AuthResult result = authenticateUserWithDetails(token);
        return result.isSuccess() ? result.getUserId() : null;
    }

    @Override
    public AuthResult authenticateUserWithDetails(String token) {
        try {
            if (token == null || token.trim().isEmpty()) {
                return AuthResult.failure("认证令牌为空", "EMPTY_TOKEN");
            }

            // 检查token是否在黑名单中
            String blacklistKey = "chat:token:blacklist:" + token;
            if (Boolean.TRUE.equals(redisTemplate.hasKey(blacklistKey))) {
                return AuthResult.failure("认证令牌已失效", "TOKEN_REVOKED");
            }

            // 调用auth-service验证JWT token
            R<UserInfo> validationResponse = jwtValidationService.validateToken(token);

            if (!validationResponse.isSuccess() || validationResponse.getData() == null) {
                log.warn("JWT token验证失败: message={}", validationResponse.getMessage());
                return AuthResult.failure(validationResponse.getMessage(), "TOKEN_VALIDATION_FAILED");
            }

            UserInfo userInfo = validationResponse.getData();

            // 检查用户状态（本地缓存）
            String userStatusKey = "chat:user:status:" + userInfo.getId();
            Object statusObj = redisTemplate.opsForValue().get(userStatusKey);
            if ("disabled".equals(statusObj)) {
                return AuthResult.userDisabled();
            }

            log.info("JWT认证成功: username={}, userId={}",
                    userInfo.getUsername(), userInfo.getId());

            return AuthResult.success(
                    userInfo.getId(),
                    userInfo.getUsername(),
                    userInfo.getNickname(),
                    userInfo.getAvatarUrl()
            );

        } catch (Exception e) {
            log.error("JWT认证失败", e);
            return AuthResult.failure("认证处理异常", "INTERNAL_ERROR");
        }
    }

    @Override
    public int getUserConnectionCount(Long userId) {
        try {
            return (int) channelConnections.values().stream()
                .filter(conn -> Objects.equals(conn.getUserId(), userId))
                .count();
        } catch (Exception e) {
            log.error("获取用户连接数失败: userId={}", userId, e);
            return 0;
        }
    }

    @Override
    public void joinRoom(Channel channel, String roomId) {
        UserConnection connection = channelConnections.get(channel);
        if (connection == null) {
            log.warn("用户连接不存在，无法加入房间: channelId={}, roomId={}", 
                channel.id().asShortText(), roomId);
            return;
        }

        Long userId = connection.getUserId();
        joinRoomInternal(userId, roomId);
        connection.joinRoom(roomId);

        log.info("用户加入房间: userId={}, roomId={}", userId, roomId);
    }

    @Override
    public void leaveRoom(Channel channel, String roomId) {
        UserConnection connection = channelConnections.get(channel);
        if (connection == null) {
            log.warn("用户连接不存在，无法离开房间: channelId={}, roomId={}",
                channel.id().asShortText(), roomId);
            return;
        }

        Long userId = connection.getUserId();
        leaveRoomInternal(userId, roomId);
        connection.leaveRoom(roomId);

        log.info("用户离开房间: userId={}, roomId={}", userId, roomId);
    }

    @Override
    public void removeUserFromRoom(String roomId, Long userId) {
        try {
            // 内部离开房间处理
            leaveRoomInternal(userId, roomId);

            // 从用户连接中移除房间
            for (UserConnection connection : channelConnections.values()) {
                if (Objects.equals(connection.getUserId(), userId)) {
                    connection.leaveRoom(roomId);
                    break;
                }
            }

            // 如果用户在线，发送强制离开消息
            if (isUserOnline(userId)) {
                WebSocketMessage leaveMessage = WebSocketMessage.builder()
                        .type("force_leave_room")
                        .data(Map.of(
                            "roomId", roomId,
                            "reason", "被管理员移出房间"
                        ))
                        .timestamp(System.currentTimeMillis())
                        .build();
                sendMessageToUser(userId, leaveMessage);
            }

            log.info("强制用户离开房间: userId={}, roomId={}", userId, roomId);

        } catch (Exception e) {
            log.error("强制用户离开房间失败: userId={}, roomId={}", userId, roomId, e);
        }
    }

    @Override
    public Set<Long> getRoomUsers(String roomId) {
        Set<Long> users = roomUsers.get(roomId);
        return users != null ? new HashSet<>(users) : new HashSet<>();
    }

    @Override
    public Set<String> getUserRooms(Long userId) {
        Set<String> rooms = userRooms.get(userId);
        return rooms != null ? new HashSet<>(rooms) : new HashSet<>();
    }

    @Override
    public boolean sendMessageToUser(Long userId, WebSocketMessage message) {
        UserConnection connection = userConnections.get(userId);
        if (connection == null || !connection.getChannel().isActive()) {
            return false;
        }

        try {
            String json = JSON.toJSONString(message);
            connection.getChannel().writeAndFlush(new TextWebSocketFrame(json));
            connection.updateLastActiveTime();
            return true;
        } catch (Exception e) {
            log.error("发送消息给用户失败: userId={}, message={}", userId, message, e);
            return false;
        }
    }

    @Override
    public int broadcastToRoom(String roomId, WebSocketMessage message, Long excludeUserId) {
        Set<Long> users = roomUsers.get(roomId);
        if (users == null || users.isEmpty()) {
            return 0;
        }

        int sentCount = 0;
        for (Long userId : users) {
            if (excludeUserId != null && excludeUserId.equals(userId)) {
                continue;
            }

            if (sendMessageToUser(userId, message)) {
                sentCount++;
            }
        }

        return sentCount;
    }

    @Override
    public int broadcastToAll(WebSocketMessage message, Long excludeUserId) {
        int sentCount = 0;
        for (Long userId : userConnections.keySet()) {
            if (excludeUserId != null && excludeUserId.equals(userId)) {
                continue;
            }

            if (sendMessageToUser(userId, message)) {
                sentCount++;
            }
        }

        return sentCount;
    }

    @Override
    public int getOnlineUserCount() {
        return userConnections.size();
    }

    @Override
    public Set<Long> getOnlineUserIds() {
        return new HashSet<>(userConnections.keySet());
    }

    @Override
    public boolean isUserOnline(Long userId) {
        UserConnection connection = userConnections.get(userId);
        return connection != null && connection.getChannel().isActive();
    }

    @Override
    public List<UserConnection> getAllConnections() {
        return new ArrayList<>(userConnections.values());
    }

    @Override
    public void cleanupInactiveConnections() {
        LocalDateTime cutoffTime = LocalDateTime.now().minusMinutes(30);
        
        List<Channel> inactiveChannels = channelConnections.values().stream()
                .filter(conn -> conn.getLastActiveTime().isBefore(cutoffTime) || !conn.getChannel().isActive())
                .map(UserConnection::getChannel)
                .collect(Collectors.toList());

        for (Channel channel : inactiveChannels) {
            removeConnection(channel);
        }

        log.info("清理无效连接完成，清理数量: {}", inactiveChannels.size());
    }

    @Override
    public RoomStats getRoomStats(String roomId) {
        RoomStats stats = new RoomStats();
        stats.setRoomId(roomId);
        
        Set<Long> users = roomUsers.get(roomId);
        stats.setUserCount(users != null ? users.size() : 0);
        
        // 从Redis获取房间消息数量
        try {
            String key = "chat:room:stats:" + roomId;
            Object messageCount = redisTemplate.opsForHash().get(key, "messageCount");
            stats.setMessageCount(messageCount != null ? Long.parseLong(messageCount.toString()) : 0);
            
            Object createTime = redisTemplate.opsForHash().get(key, "createTime");
            stats.setCreateTime(createTime != null ? Long.parseLong(createTime.toString()) : System.currentTimeMillis());
        } catch (Exception e) {
            log.error("获取房间统计信息失败: roomId={}", roomId, e);
        }
        
        return stats;
    }

    /**
     * 内部方法：用户加入房间
     */
    private void joinRoomInternal(Long userId, String roomId) {
        roomUsers.computeIfAbsent(roomId, k -> ConcurrentHashMap.newKeySet()).add(userId);
        userRooms.computeIfAbsent(userId, k -> ConcurrentHashMap.newKeySet()).add(roomId);
        
        // 同步到Redis
        try {
            String roomKey = "chat:room:users:" + roomId;
            redisTemplate.opsForSet().add(roomKey, userId.toString());
            redisTemplate.expire(roomKey, 1, TimeUnit.DAYS);
            
            String userKey = "chat:user:rooms:" + userId;
            redisTemplate.opsForSet().add(userKey, roomId);
            redisTemplate.expire(userKey, 1, TimeUnit.DAYS);
        } catch (Exception e) {
            log.error("同步用户房间信息到Redis失败: userId={}, roomId={}", userId, roomId, e);
        }
    }

    /**
     * 内部方法：用户离开房间
     */
    private void leaveRoomInternal(Long userId, String roomId) {
        Set<Long> users = roomUsers.get(roomId);
        if (users != null) {
            users.remove(userId);
            if (users.isEmpty()) {
                roomUsers.remove(roomId);
            }
        }
        
        Set<String> rooms = userRooms.get(userId);
        if (rooms != null) {
            rooms.remove(roomId);
            if (rooms.isEmpty()) {
                userRooms.remove(userId);
            }
        }
        
        // 从Redis中移除
        try {
            String roomKey = "chat:room:users:" + roomId;
            redisTemplate.opsForSet().remove(roomKey, userId.toString());
            
            String userKey = "chat:user:rooms:" + userId;
            redisTemplate.opsForSet().remove(userKey, roomId);
        } catch (Exception e) {
            log.error("从Redis移除用户房间信息失败: userId={}, roomId={}", userId, roomId, e);
        }
    }

    /**
     * 同步连接信息到Redis
     */
    private void syncConnectionToRedis(UserConnection connection) {
        try {
            String key = "chat:connection:" + connection.getUserId();
            Map<String, Object> connectionInfo = new HashMap<>();
            connectionInfo.put("userId", connection.getUserId());
            connectionInfo.put("connectTime", connection.getConnectTime().toString());
            connectionInfo.put("lastActiveTime", connection.getLastActiveTime().toString());
            connectionInfo.put("status", connection.getStatus().name());
            connectionInfo.put("ipAddress", connection.getIpAddress());
            
            redisTemplate.opsForHash().putAll(key, connectionInfo);
            redisTemplate.expire(key, 1, TimeUnit.DAYS);
        } catch (Exception e) {
            log.error("同步连接信息到Redis失败: userId={}", connection.getUserId(), e);
        }
    }

    /**
     * 从Redis移除连接信息
     */
    private void removeConnectionFromRedis(Long userId) {
        try {
            String key = "chat:connection:" + userId;
            redisTemplate.delete(key);
        } catch (Exception e) {
            log.error("从Redis移除连接信息失败: userId={}", userId, e);
        }
    }



    /**
     * 获取Channel的远程地址
     */
    private String getChannelRemoteAddress(Channel channel) {
        try {
            return channel.remoteAddress().toString();
        } catch (Exception e) {
            return "unknown";
        }
    }

    // 注意：JWT token验证和用户信息获取现在通过JwtValidationService和Feign客户端处理
    // 不再需要本地解析JWT token或模拟用户信息
}
