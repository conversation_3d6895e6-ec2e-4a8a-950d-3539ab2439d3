package com.xiang.chat.service;

import com.xiang.chat.code.R;
import com.xiang.chat.dto.UserInfo;
import com.xiang.chat.dto.ValidationResult;
import com.xiang.chat.feign.AuthFeignService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

/**
 * JWT验证服务
 * 使用Feign客户端调用auth-service验证JWT token
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class JwtValidationService {

    private final AuthFeignService authFeignService;

    /**
     * 验证JWT token并获取用户信息
     */
    public R<UserInfo> validateToken(String token) {
        try {
            if (token == null || token.trim().isEmpty()) {
                return R.error("Token不能为空");
            }

            // 通过Feign客户端调用auth-service验证接口
            R<ValidationResult> response = authFeignService.validateToken(token);

            if (response.isSuccess() && response.getData() != null) {
                ValidationResult validationResult = response.getData();
                UserInfo userInfo = validationResult.getUser();
                if (userInfo != null) {
                    return R.success(userInfo);
                }
            }

            // 如果验证失败，返回错误信息
            return R.error(response.getMessage() != null ? response.getMessage() : "Token验证失败");

        } catch (Exception e) {
            log.error("JWT validation error via Feign", e);
            return R.error("Token验证异常: " + e.getMessage());
        }
    }

    /**
     * 获取当前用户信息
     */
    @SuppressWarnings("unchecked")
    public R<UserInfo> getCurrentUser() {
        try {
            Map<String, Object> response = authFeignService.getUserInfo();

            if (response != null) {
                Boolean authenticated = (Boolean) response.get("authenticated");

                if (Boolean.TRUE.equals(authenticated)) {
                    Map<String, Object> userInfoMap = (Map<String, Object>) response.get("user");
                    if (userInfoMap != null) {
                        UserInfo userInfo = new UserInfo(
                                ((Number) userInfoMap.get("id")).longValue(),
                                (String) userInfoMap.get("username"),
                                (String) userInfoMap.get("nickname"),
                                (String) userInfoMap.get("avatarUrl"));
                        return R.success(userInfo);
                    }
                }

                String message = (String) response.get("message");
                return R.error(message != null ? message : "用户未认证");
            }

            return R.error("获取用户信息失败");

        } catch (Exception e) {
            log.error("Get current user error via Feign", e);
            return R.error("获取用户信息异常: " + e.getMessage());
        }
    }

}